"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Alert<PERSON>riangle, Loader, Check } from "lucide-react"
import { toast } from "sonner"
import type { Employee, Department, Manager, UserSession } from "@/lib/types"
import { employeeFormSchema, type Employee as EmployeeSchema } from "@/lib/schemas"
import { saveEmployeeAction } from "@/lib/actions"
import { assignManagersToEmployeeAction } from "@/lib/actions/employees"
import { useTransition } from "react"
import { MultiManagerSelector } from "./multi-manager-selector"

interface EmployeeFormDialogProps {
  isOpen: boolean
  onClose: () => void
  employee: Employee | null
  departments: Department[]
  managers: Manager[]
  user?: UserSession | null
}

type EmployeeFormData = Omit<EmployeeSchema, 'id'>

interface SelectedManager {
  id: string
  name: string
  isPrimary: boolean
}

export function EmployeeFormDialog({ isOpen, onClose, employee, departments, managers, user }: EmployeeFormDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = React.useState<string | null>(null)
  const [isSuccess, setIsSuccess] = React.useState(false)
  const [selectedManagers, setSelectedManagers] = React.useState<SelectedManager[]>([])


  const form = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeFormSchema),
    defaultValues: {
      fullName: "",
      email: "",
      role: "",
      linkedinUrl: "",
      twitterUrl: "",
      telegramUrl: "",
      departmentId: "",
      managerId: "none",
      rate: "monthly",
      active: true,
    },
  })

  // Reset form when dialog opens/closes or employee changes
  React.useEffect(() => {
    if (isOpen) {
      setSubmitError(null)
      setIsSuccess(false)
      if (employee) {
        form.reset({
          fullName: employee.fullName,
          email: employee.email || "",
          role: employee.role || "",
          linkedinUrl: employee.linkedinUrl || "",
          twitterUrl: employee.twitterUrl || "",
          telegramUrl: employee.telegramUrl || "",
          departmentId: employee.departmentId,
          managerId: employee.managerId || "none",
          rate: employee.compensation,
          active: employee.active,
        })

        // Set selected managers from employee data
        const currentManagers: SelectedManager[] = []

        // Handle multiple managers if available
        if (employee.managers && employee.managers.length > 0) {
          employee.managers.forEach(manager => {
            currentManagers.push({
              id: manager.managerId,
              name: manager.managerName || 'Unknown Manager',
              isPrimary: manager.isPrimary
            })
          })
        } else if (employee.managerId && employee.managerName) {
          // Handle legacy single manager
          currentManagers.push({
            id: employee.managerId,
            name: employee.managerName,
            isPrimary: true
          })
        }

        setSelectedManagers(currentManagers)
      } else {
        form.reset({
          fullName: "",
          email: "",
          role: "",
          linkedinUrl: "",
          twitterUrl: "",
          telegramUrl: "",
          departmentId: "",
          managerId: "none",
          rate: "monthly",
          active: true,
        })
        setSelectedManagers([])
      }
    }
  }, [employee, isOpen, form])

  const onSubmit = async (data: EmployeeFormData) => {
    setSubmitError(null)

    startTransition(async () => {
      try {
        // Create FormData for server action
        const formData = new FormData()
        if (employee?.id) {
          formData.append('id', employee.id)
        }
        formData.append('fullName', data.fullName)
        formData.append('email', data.email)
        formData.append('role', data.role || '')
        formData.append('linkedinUrl', data.linkedinUrl || '')
        formData.append('twitterUrl', data.twitterUrl || '')
        formData.append('telegramUrl', data.telegramUrl || '')
        formData.append('departmentId', data.departmentId)
        formData.append('managerId', data.managerId === "none" ? '' : (data.managerId || ''))
        formData.append('rate', data.rate)
        
        formData.append('active', data.active.toString())

        const result = await saveEmployeeAction(formData)

        if (result.success) {
          // If we have selected managers, assign them
          if (selectedManagers.length > 0) {
            let employeeId = employee?.id

            // For new employees, we need to find the employee by email since saveEmployeeAction doesn't return ID
            if (!employeeId) {
              // Wait a moment for the employee to be created, then find by email
              await new Promise(resolve => setTimeout(resolve, 500))
              try {
                const { getEmployees } = await import('@/lib/data/employees')
                const employees = await getEmployees()
                const newEmployee = employees.find(emp => emp.email === data.email)
                employeeId = newEmployee?.id
              } catch (error) {
                console.warn('Could not find newly created employee for manager assignment:', error)
              }
            }

            if (employeeId) {
              const managerIds = selectedManagers.map(m => m.id)
              const primaryManagerId = selectedManagers.find(m => m.isPrimary)?.id

              const managerResult = await assignManagersToEmployeeAction(
                employeeId,
                managerIds,
                primaryManagerId
              )

              if (!managerResult.success) {
                console.warn('Failed to assign managers:', managerResult.error)
                // Don't fail the whole operation, just warn
              }
            }
          }

          setIsSuccess(true)
          const actionType = employee ? "updated" : "added"
          const managerCount = selectedManagers.length
          const managerText = managerCount > 0 ? ` with ${managerCount} manager(s)` : ''

          toast.success(`Employee ${actionType} successfully!`, {
            description: `${data.fullName} has been ${actionType} to the system${managerText}.`
          })

          // Brief delay to show success state before closing
          setTimeout(() => {
            onClose()
          }, 1500)
        } else {
          setSubmitError('error' in result ? result.error : 'Failed to save employee')
          toast.error('Failed to save employee', {
            description: 'error' in result ? result.error : 'An error occurred while saving the employee.'
          })
        }
      } catch (error) {
        console.error('Form submission error:', error)
        setSubmitError('An unexpected error occurred. Please try again.')
        toast.error('Unexpected error', {
          description: 'An unexpected error occurred. Please try again.'
        })
      }
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] sm:w-full max-w-[600px] max-h-[90vh] overflow-y-auto p-4 sm:p-6">
        <DialogHeader className="space-y-2 sm:space-y-3">
          <DialogTitle className="text-lg sm:text-xl">Add New Employee</DialogTitle>
          <DialogDescription className="text-sm sm:text-base">
            Add a new employee to the system. Fields marked with * are required. For editing existing employees, use the profile edit page.
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 sm:space-y-6">
            {/* Basic Information Section */}
            <div className="space-y-3 sm:space-y-4">
              <div className="border-b pb-2">
                <h3 className="text-sm font-medium text-muted-foreground">Basic Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Full Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter employee's full name"
                          {...field}
                          disabled={isPending}
                          className="min-h-[44px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address *</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Enter employee's email address"
                          {...field}
                          disabled={isPending}
                          className="min-h-[44px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role/Position *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g. Software Engineer, Marketing Manager"
                        {...field}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Social Media Links Section */}
            <div className="space-y-4">
              <div className="border-b pb-2">
                <h3 className="text-sm font-medium text-muted-foreground">Social Media Links (Optional)</h3>
              </div>

              <div className="grid grid-cols-1 gap-4">
                <FormField
                  control={form.control}
                  name="linkedinUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>LinkedIn URL</FormLabel>
                      <FormControl>
                        <Input
                          type="url"
                          placeholder="https://linkedin.com/in/username"
                          {...field}
                          disabled={isPending}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="twitterUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Twitter/X URL</FormLabel>
                        <FormControl>
                          <Input
                            type="url"
                            placeholder="https://twitter.com/username"
                            {...field}
                            disabled={isPending}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="telegramUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Telegram URL</FormLabel>
                        <FormControl>
                          <Input
                            type="url"
                            placeholder="https://t.me/username"
                            {...field}
                            disabled={isPending}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            {/* Organization Assignment Section */}
            <div className="space-y-4">
              <div className="border-b pb-2">
                <h3 className="text-sm font-medium text-muted-foreground">Organization Assignment</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
                <FormField
                  control={form.control}
                  name="departmentId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Department *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isPending}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a department" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {departments.map((dept) => (
                            <SelectItem key={dept.id} value={dept.id}>
                              {dept.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="rate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payment Frequency *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={isPending}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment frequency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="hourly">Hourly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Multi-Manager Assignment */}
              <div className="space-y-2">
                <div className="space-y-1">
                  <label className="text-sm font-medium">
                    Managers (Optional)
                  </label>
                  <p className="text-xs text-muted-foreground">
                    You can assign multiple managers. The first one selected will be the primary manager.
                  </p>
                </div>
                <MultiManagerSelector
                  managers={managers}
                  selectedManagers={selectedManagers}
                  onManagersChange={setSelectedManagers}
                  disabled={isPending}
                  placeholder="Select managers for this employee..."
                />
              </div>
            </div>


            <DialogFooter className="gap-3 pt-4 sm:pt-6 border-t flex-col sm:flex-row">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isPending}
                className="w-full sm:w-auto min-h-[44px] order-2 sm:order-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isPending || isSuccess}
                className={`w-full sm:w-auto min-h-[44px] order-1 sm:order-2 ${isSuccess ? "bg-green-600 hover:bg-green-600" : ""}`}
              >
                {isSuccess ? (
                  <>
                    <Check className="mr-2 h-4 w-4 animate-pulse" />
                    Success!
                  </>
                ) : isPending ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  employee ? "Update Employee" : "Create Employee"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
