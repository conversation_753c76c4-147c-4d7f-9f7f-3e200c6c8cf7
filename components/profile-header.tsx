"use client"

import { Em<PERSON>loyee } from "@/lib/types"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { getEmployeeDepartmentDisplay, formatMultiDepartmentDisplay } from "@/lib/utils"
import {
  Edit,
  Share2,
  Copy,
  Check,
  Mail,
  Building,
  Linkedin,
  Twitter,
  MessageCircle,
  Users
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"

interface ProfileHeaderProps {
  employee: Employee
  canEdit?: boolean
  currentUserRole?: string
}

export function ProfileHeader({ 
  employee, 
  canEdit = false,
  currentUserRole
}: ProfileHeaderProps) {
  const [copied, setCopied] = useState(false)

  const handleShareProfile = async () => {
    const shareUrl = `${window.location.origin}/profile/${employee.id}`
    try {
      await navigator.clipboard.writeText(shareUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy link:', err)
    }
  }

  const getInitials = (firstName?: string, lastName?: string, fullName?: string) => {
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
    }
    if (fullName) {
      const names = fullName.split(' ')
      if (names.length >= 2) {
        return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`.toUpperCase()
      }
      return fullName.charAt(0).toUpperCase()
    }
    return 'U'
  }

  const displayName = employee.firstName && employee.lastName 
    ? `${employee.firstName} ${employee.lastName}`
    : employee.fullName || 'Unknown Employee'

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg">
      <CardHeader className="pb-0">
        <div className="flex flex-col lg:flex-row items-start gap-6">
          {/* Avatar Section */}
          <div className="flex flex-col sm:flex-row items-center sm:items-start gap-4 w-full lg:w-auto">
            <Avatar className="h-24 w-24 ring-4 ring-background shadow-lg transition-transform duration-300 hover:scale-105">
              <AvatarImage src="" alt={displayName} />
              <AvatarFallback className="text-2xl font-semibold bg-gradient-to-br from-blue-500 to-purple-600 text-white transition-all duration-300">
                {getInitials(employee.firstName, employee.lastName, employee.fullName)}
              </AvatarFallback>
            </Avatar>
            
            {/* Name and Department - Mobile/Tablet Layout */}
            <div className="flex flex-col items-center sm:items-start text-center sm:text-left lg:hidden">
              <h1 className="text-2xl sm:text-3xl font-bold text-foreground">
                {displayName}
              </h1>
              <div className="flex items-center gap-2 mt-1">
                <Building className="h-4 w-4 text-muted-foreground" />
                <Badge variant="secondary" className="text-sm">
                  {formatMultiDepartmentDisplay(employee)}
                </Badge>
              </div>
            </div>
          </div>

          {/* Name and Department - Desktop Layout */}
          <div className="hidden lg:flex lg:flex-col lg:justify-center lg:flex-1">
            <h1 className="text-3xl xl:text-4xl font-bold text-foreground mb-2">
              {displayName}
            </h1>
            <div className="flex items-center gap-3">
              <Building className="h-5 w-5 text-muted-foreground" />
              <Badge variant="secondary" className="text-base px-3 py-1">
                {formatMultiDepartmentDisplay(employee)}
              </Badge>
              <Badge 
                variant={employee.active ? "default" : "secondary"} 
                className="text-sm px-3 py-1"
              >
                {employee.active ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 w-full lg:w-auto justify-center lg:justify-end">
            {canEdit && (
              <Link href={`/dashboard/employees/${employee.id}/profile/edit`}>
                <Button variant="outline" size="default" className="flex items-center gap-2 transition-all duration-200 hover:shadow-md">
                  <Edit className="h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
                  Edit Profile
                </Button>
              </Link>
            )}
            <Button 
              variant="outline" 
              size="default" 
              onClick={handleShareProfile}
              className="flex items-center gap-2 transition-all duration-200 hover:shadow-md"
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4" />
                  Copied!
                </>
              ) : (
                <>
                  <Share2 className="h-4 w-4" />
                  Share
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-6">
        {/* Contact and Social Information */}
        <div className="space-y-4">
          {/* Contact Row */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-8">
            {employee.email && (
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-full bg-blue-50 dark:bg-blue-950">
                  <Mail className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Email</p>
                  <a 
                    href={`mailto:${employee.email}`} 
                    className="text-sm font-medium hover:underline text-blue-600 dark:text-blue-400"
                  >
                    {employee.email}
                  </a>
                </div>
              </div>
            )}
            
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-green-50 dark:bg-green-950">
                <Building className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Employment</p>
                <p className="text-sm font-medium capitalize">
                  {employee.compensation}
                </p>
              </div>
            </div>
          </div>

          {/* Manager Information */}
          {employee.managers && employee.managers.length > 0 && (
            <>
              <Separator />
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <div className="p-1 rounded-full bg-green-50 dark:bg-green-950">
                    <Users className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <p className="text-sm text-muted-foreground">Reports To</p>
                </div>
                <div className="space-y-2">
                  {employee.managers.map((manager, index) => (
                    <div key={manager.id} className="flex items-center justify-between">
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{manager.managerName}</span>
                          {manager.isPrimary && (
                            <Badge variant="outline" className="text-xs">Primary</Badge>
                          )}
                        </div>
                        {manager.departmentName && (
                          <span className="text-xs text-muted-foreground">
                            {manager.departmentName} Department
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Social Links */}
          {(employee.linkedinUrl || employee.twitterUrl || employee.telegramUrl) && (
            <>
              <Separator />
              <div>
                <p className="text-sm text-muted-foreground mb-3">Social Media</p>
                <div className="flex flex-wrap gap-3">
                  {employee.linkedinUrl && (
                    <a 
                      href={employee.linkedinUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 px-3 py-2 rounded-lg bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900 transition-all duration-200 hover:shadow-sm hover:scale-105"
                    >
                      <Linkedin className="h-4 w-4" />
                      <span className="text-sm font-medium">LinkedIn</span>
                    </a>
                  )}
                  {employee.twitterUrl && (
                    <a 
                      href={employee.twitterUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 px-3 py-2 rounded-lg bg-slate-50 dark:bg-slate-900 text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 transition-all duration-200 hover:shadow-sm hover:scale-105"
                    >
                      <Twitter className="h-4 w-4" />
                      <span className="text-sm font-medium">Twitter</span>
                    </a>
                  )}
                  {employee.telegramUrl && (
                    <a 
                      href={employee.telegramUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 px-3 py-2 rounded-lg bg-sky-50 dark:bg-sky-950 text-sky-700 dark:text-sky-300 hover:bg-sky-100 dark:hover:bg-sky-900 transition-all duration-200 hover:shadow-sm hover:scale-105"
                    >
                      <MessageCircle className="h-4 w-4" />
                      <span className="text-sm font-medium">Telegram</span>
                    </a>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Mobile Status Badges - Only show on smaller screens */}
          <div className="flex justify-center gap-3 lg:hidden">
            <Badge 
              variant={employee.active ? "default" : "secondary"} 
              className="text-sm px-3 py-1"
            >
              {employee.active ? "Active" : "Inactive"}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}