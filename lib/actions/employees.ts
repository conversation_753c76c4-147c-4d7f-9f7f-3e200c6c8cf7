"use server"

import { revalidatePath } from "next/cache"
import { saveEmployee } from "../data/index"
import { employeeFormSchema } from "../schemas"
import { db } from "../db"
import { supabaseAdminQuery } from "../supabase"
import {
  requirePermission,
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError,
  ValidationError
} from "./shared"

export async function saveEmployeeAction(formData: FormData) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-save', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check
    await requirePermission('employee:write')

    // Extract and validate data
    const rawData = {
      id: formData.get('id') as string || undefined,
      fullName: formData.get('fullName') as string,
      email: formData.get('email') as string,
      role: formData.get('role') as string || undefined,
      linkedinUrl: formData.get('linkedinUrl') as string || undefined,
      twitterUrl: formData.get('twitterUrl') as string || undefined,
      telegramUrl: formData.get('telegramUrl') as string || undefined,
      compensation: formData.get('compensation') as 'hourly' | 'monthly',
      rate: Number(formData.get('rate')),
      departmentId: formData.get('departmentId') as string,
      managerId: formData.get('managerId') as string || null,
      active: formData.get('active') === 'true',
    }

    const validatedData = employeeFormSchema.parse(rawData)

    // Log the action
    await logUserAction('employee:save', {
      employeeName: validatedData.fullName,
      isUpdate: !!rawData.id
    })

    // Save to database and get the result
    const savedEmployee = await saveEmployee(validatedData)

    // Revalidate cache
    revalidatePath("/dashboard/employees")

    return {
      success: true,
      message: "Employee saved successfully.",
      employee: savedEmployee
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function deleteEmployeeAction(employeeId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-delete', 3, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check (only super-admin and hr-admin can delete)
    await requirePermission('employee:delete')

    // Validate employee ID
    if (!employeeId || typeof employeeId !== 'string') {
      throw new ValidationError("Valid employee ID is required")
    }

    // Log the action
    await logUserAction('employee:delete', { employeeId })

    // Soft delete employee (set active to false)
    await db.softDeleteEmployee(employeeId)

    // Revalidate cache
    revalidatePath("/dashboard/employees")

    return { success: true, message: "Employee deactivated successfully." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function removeEmployeeAction(employeeId: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-remove', 2, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check (only super-admin can permanently remove)
    await requirePermission('employee:delete')

    // Validate employee ID
    if (!employeeId || typeof employeeId !== 'string') {
      throw new ValidationError("Valid employee ID is required")
    }

    // Log the action
    await logUserAction('employee:remove', { employeeId })

    // Hard delete employee (permanently remove from database)
    await db.hardDeleteEmployee(employeeId)

    // Revalidate cache
    revalidatePath("/dashboard/employees")

    return { success: true, message: "Employee permanently removed from system." }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function assignManagersToEmployeeAction(
  employeeId: string,
  managerIds: string[],
  primaryManagerId?: string
): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    console.log('🔧 [ASSIGN MANAGERS] Starting manager assignment process')
    console.log('🔧 [ASSIGN MANAGERS] Input parameters:', { employeeId, managerIds, primaryManagerId })

    const session = await validateSession()
    console.log('🔧 [ASSIGN MANAGERS] Session validated for user:', session.userId)

    await requirePermission('employee:write')
    console.log('🔧 [ASSIGN MANAGERS] Permission check passed')

    if (!employeeId || !managerIds || managerIds.length === 0) {
      console.error('🔧 [ASSIGN MANAGERS] Invalid input parameters')
      throw new ValidationError('Employee ID and at least one manager ID required')
    }

    // Remove existing manager assignments
    console.log('🔧 [ASSIGN MANAGERS] Fetching existing manager assignments...')
    const existingManagers = await db.getEmployeeManagers(employeeId)
    console.log('🔧 [ASSIGN MANAGERS] Found', existingManagers.length, 'existing manager assignments:', existingManagers.map(m => ({ id: m.managerId, name: m.managerName, isPrimary: m.isPrimary })))

    for (const manager of existingManagers) {
      console.log('🔧 [ASSIGN MANAGERS] Removing existing manager assignment:', manager.managerId, manager.managerName)
      await db.removeManagerFromEmployee(employeeId, manager.managerId)
    }
    console.log('🔧 [ASSIGN MANAGERS] Removed all existing manager assignments')

    console.log('🔧 [ASSIGN MANAGERS] Adding new manager assignments:', { employeeId, managerIds, primaryManagerId })

    // Add new manager assignments
    for (const managerId of managerIds) {
      const isPrimary = managerId === primaryManagerId
      console.log('🔧 [ASSIGN MANAGERS] Assigning manager:', { managerId, isPrimary })
      await db.assignManagerToEmployee(employeeId, managerId, isPrimary)
    }

    // Sync legacy manager_id field with primary manager for backward compatibility
    console.log('🔧 [ASSIGN MANAGERS] Syncing legacy manager_id field...')
    const { error: updateError } = await supabaseAdminQuery
      .employees()
      .update({ manager_id: primaryManagerId || null })
      .eq('id', employeeId)

    if (updateError) {
      console.error('🔧 [ASSIGN MANAGERS] Failed to sync legacy manager_id:', updateError)
      // Don't fail the whole operation, just log the warning
      console.warn('🔧 [ASSIGN MANAGERS] Manager assignment succeeded but legacy field sync failed')
    } else {
      console.log('🔧 [ASSIGN MANAGERS] Successfully synced legacy manager_id to:', primaryManagerId || null)
    }

    await logUserAction('employee:assign-managers', {
      employeeId,
      managerIds,
      primaryManagerId,
      userId: session.userId
    })

    revalidatePath("/dashboard/employees")
    revalidatePath("/dashboard")

    console.log('🔧 [ASSIGN MANAGERS] Manager assignment completed successfully')
    return {
      success: true,
      message: `Successfully assigned ${managerIds.length} managers to employee`
    }
  } catch (error) {
    return handleServerActionError(error)
  }
}

export async function updateEmployeeProfile(
  employeeId: string,
  data: {
    first_name?: string | null
    last_name?: string | null
    email?: string | null
    role?: string | null
    bio?: string | null
    linkedin_url?: string | null
    twitter_url?: string | null
    telegram_url?: string | null
    department_id?: string | null
    manager_id?: string | null
  }
) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'employee-profile-update', 10, 60000)) {
      throw new RateLimitError()
    }

    // Authorization check - user can edit their own profile or have permission
    const currentUserId = session.userId
    if (employeeId !== currentUserId) {
      await requirePermission('employee:write')
    }

    // Validate employee ID
    if (!employeeId || typeof employeeId !== 'string') {
      throw new ValidationError("Valid employee ID is required")
    }

    // Import validation utilities
    const { validateProfileData } = await import('../validation')

    // Validate and sanitize the profile data
    const validation = validateProfileData({
      firstName: data.first_name || undefined,
      lastName: data.last_name || undefined,
      email: data.email || undefined,
      role: data.role || undefined,
      bio: data.bio || undefined,
      linkedinUrl: data.linkedin_url || undefined,
      twitterUrl: data.twitter_url || undefined,
      telegramUrl: data.telegram_url || undefined,
    })

    if (!validation.isValid) {
      throw new ValidationError(`Validation failed: ${validation.errors.join(', ')}`)
    }

    // Prepare update data with sanitized values and computed full_name
    const updateData = {
      ...validation.sanitizedData,
      full_name: `${validation.sanitizedData.first_name} ${validation.sanitizedData.last_name}`.trim()
    }

    if (process.env.ENABLE_DEBUG_LOGS === 'true') {
      console.log('🔧 [DEBUG] updateEmployeeProfile - Update data:', updateData)
    }

    // Update in database
    const { error } = await supabaseAdminQuery
      .employees()
      .update(updateData)
      .eq('id', employeeId)

    if (error) {
      console.error("Error updating employee profile:", error)
      throw new Error("Failed to update employee profile")
    }

    // Log the action
    await logUserAction('employee:update-profile', { 
      employeeId,
      updatedFields: Object.keys(updateData)
    })

    // Revalidate cache
    revalidatePath(`/dashboard/employees/${employeeId}/profile`)
    revalidatePath('/dashboard/employees')
    
    return { success: true, message: "Profile updated successfully" }
  } catch (error) {
    return handleServerActionError(error)
  }
}